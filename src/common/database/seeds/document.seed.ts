import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { AssignmentGroup } from '@src/modules/assignment-groups/entities/assignment-group.entity';
import {
  Document,
  DocumentStatus,
  DocumentRepositoryType,
} from '@modules/documents/entities/document.entity';
import { Thread } from '@modules/threads/entities/thread.entity';

export async function seedDocuments(
  dataSource: DataSource,
  users: User[],
  assignmentGroups: AssignmentGroup[],
): Promise<Document[]> {
  console.log('🌱 Seeding documents...');
  const documentRepository = dataSource.getRepository(Document);

  // Check if documents already exist
  const existingDocuments = await documentRepository.find();
  if (existingDocuments.length > 0) {
    console.log('✅ Documents already exist, skipping document seeding');
    return existingDocuments;
  }

  const instructor = users.find((u) => u.name === '<PERSON>. <PERSON>')!;
  const student1 = users.find((u) => u.name === 'Alex')!;
  const student2 = users.find((u) => u.name === 'Watson')!;
  const student3 = users.find((u) => u.name === 'Sarah')!;

  // Fetch real thread IDs from MongoDB
  const threads = await Thread.find().limit(10); // Get some threads for seeding
  const threadIds = threads.map((thread) => String(thread._id));

  const documents = [
    // Instructor-uploaded permanent files
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Project Requirements Document.pdf',
      status: DocumentStatus.VISIBLE,
      description: 'Official project requirements and specifications',
      tags: ['requirements', 'official', 'project-specs'],
      threadId: undefined, // Not from chat
      repositoryType: DocumentRepositoryType.PERMANENT,
      uploaderId: instructor.id,
      assignmentGroupId: assignmentGroups[0].id,
      courseId:
        assignmentGroups[0].assignment?.courseId || assignmentGroups[0].id, // Fallback
    },
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Grading Rubric.pdf',
      status: DocumentStatus.HIDDEN,
      description: 'Internal grading criteria - not visible to students',
      tags: ['grading', 'internal', 'rubric'],
      threadId: undefined,
      repositoryType: DocumentRepositoryType.PERMANENT,
      uploaderId: instructor.id,
      assignmentGroupId: assignmentGroups[0].id,
      courseId:
        assignmentGroups[0].assignment?.courseId || assignmentGroups[0].id, // Fallback
    },
    // Student-uploaded temporary files (from chat)
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Database Schema Design.pdf',
      status: DocumentStatus.VISIBLE,
      description: 'Initial database schema proposal',
      tags: ['database', 'schema', 'draft'],
      threadId: threadIds[0] || undefined, // Use real thread ID
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student1.id,
      assignmentGroupId: assignmentGroups[1].id,
      courseId:
        assignmentGroups[1].assignment?.courseId || assignmentGroups[1].id, // Fallback
    },
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'ML Model Training Data.csv',
      status: DocumentStatus.VISIBLE,
      description: 'Training dataset for predictive model',
      tags: ['machine-learning', 'dataset', 'training'],
      threadId: threadIds[1] || undefined, // Use real thread ID
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student2.id,
      assignmentGroupId: assignmentGroups[2].id,
      courseId:
        assignmentGroups[2].assignment?.courseId || assignmentGroups[2].id, // Fallback
    },
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Final Presentation.pptx',
      status: DocumentStatus.VISIBLE,
      description: 'Final project presentation slides',
      tags: ['presentation', 'final', 'slides'],
      threadId: threadIds[2] || undefined, // Use real thread ID
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student1.id,
      assignmentGroupId: assignmentGroups[0].id,
      courseId:
        assignmentGroups[0].assignment?.courseId || assignmentGroups[0].id, // Fallback
    },
    // Sarah's student upload
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Team Collaboration Notes.docx',
      status: DocumentStatus.VISIBLE,
      description: 'Meeting notes and collaboration guidelines',
      tags: ['collaboration', 'notes', 'team'],
      threadId: threadIds[1] || undefined, // Use real thread ID
      repositoryType: DocumentRepositoryType.TEMPORARY,
      uploaderId: student3.id,
      assignmentGroupId: assignmentGroups[1].id, // Team Beta where Sarah is leader
      courseId:
        assignmentGroups[1].assignment?.courseId || assignmentGroups[1].id, // Fallback
    },
    // Additional instructor permanent file
    {
      id: uuidv4(),
      fileUrl:
        'https://file-examples.com/wp-content/storage/2017/10/file-sample_150kB.pdf',
      fileName: 'Reference Materials.docx',
      status: DocumentStatus.VISIBLE,
      description: 'Additional reading materials and references',
      tags: ['reference', 'reading', 'materials'],
      threadId: undefined,
      repositoryType: DocumentRepositoryType.PERMANENT,
      uploaderId: instructor.id,
      assignmentGroupId: assignmentGroups[3].id,
      courseId:
        assignmentGroups[3].assignment?.courseId || assignmentGroups[3].id, // Fallback
    },
  ];

  const savedDocuments = await documentRepository.save(documents);
  console.log(`✅ Created ${savedDocuments.length} documents`);
  return savedDocuments;
}
