import { DataSource } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { User } from '@modules/users/entities/user.entity';
import { AssignmentGroup } from '@src/modules/assignment-groups/entities/assignment-group.entity';
import { GroupUser } from '@modules/group-users/entities/group-user.entity';

export async function seedGroupUsers(
  dataSource: DataSource,
  users: User[],
  assignmentGroups: AssignmentGroup[],
): Promise<GroupUser[]> {
  console.log('🌱 Seeding group users...');
  const groupUserRepository = dataSource.getRepository(GroupUser);

  // Check if group users already exist
  const existingGroupUsers = await groupUserRepository.find();
  if (existingGroupUsers.length > 0) {
    console.log('✅ Group users already exist, skipping group user seeding');
    return existingGroupUsers;
  }

  const instructor = users.find((u) => u.name === 'Dr. <PERSON>')!;
  const student1 = users.find((u) => u.name === '<PERSON>')!;
  const student2 = users.find((u) => u.name === 'Watson')!;
  const student3 = users.find((u) => u.name === 'Sarah')!;

  const groupUsers = [
    // Team Alpha - E-commerce Platform (instructor + students)
    {
      id: uuidv4(),
      role: 'leader',
      groupId: assignmentGroups[0].id, // Team Alpha
      userId: instructor.id,
    },
    {
      id: uuidv4(),
      role: 'member',
      groupId: assignmentGroups[0].id, // Team Alpha
      userId: student1.id,
    },
    {
      id: uuidv4(),
      role: 'member',
      groupId: assignmentGroups[0].id, // Team Alpha
      userId: student2.id,
    },
    // Team Beta - E-commerce Platform (different students)
    {
      id: uuidv4(),
      role: 'leader',
      groupId: assignmentGroups[1].id, // Team Beta
      userId: student3.id,
    },
    {
      id: uuidv4(),
      role: 'member',
      groupId: assignmentGroups[1].id, // Team Beta
      userId: student1.id,
    },
    // Team Gamma - Library Management
    {
      id: uuidv4(),
      role: 'leader',
      groupId: assignmentGroups[2].id, // Team Gamma
      userId: student2.id,
    },
    {
      id: uuidv4(),
      role: 'member',
      groupId: assignmentGroups[2].id, // Team Gamma
      userId: student3.id,
    },
    // Team Delta - ML Analytics
    {
      id: uuidv4(),
      role: 'leader',
      groupId: assignmentGroups[3].id, // Team Delta
      userId: student1.id,
    },
    {
      id: uuidv4(),
      role: 'member',
      groupId: assignmentGroups[3].id, // Team Delta
      userId: student2.id,
    },
  ];

  const savedGroupUsers = await groupUserRepository.save(groupUsers);
  console.log(`✅ Created ${savedGroupUsers.length} group users`);
  return savedGroupUsers;
}
